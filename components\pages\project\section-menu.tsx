"use client";

import React from "react";
import { cn } from "@/lib/utils";
import DeleteSection from "./section-menus/delete-section";
import { Section, Text, ExternalLink } from "@prisma/client";
import ExternalLinks from "./section-menus/external-link";
import DragSection from "./section-menus/drag-section";
import SectionSettings from "./section-menus/settings";
import CopySection from "./section-menus/copy-section";

type Props = {
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
  };
};

const SectionMenu = ({ section }: Props) => {
  return (
    <div
      className={cn(
        "absolute top-4 right-4 opacity-0 group-hover/section:opacity-100",
        "transition-all duration-300 transform translate-x-2 group-hover/section:translate-x-0",
        "bg-background/95 backdrop-blur-sm border border-border rounded-lg p-1 shadow-lg",
        "flex items-center gap-1 z-30"
      )}
    >
      {/* Move/Drag handle */}
      <DragSection section={section} />

      {/* Settings */}
      <SectionSettings section={section} />

      {/* Duplicate */}
      <CopySection section={section} />

      {/* External Link */}
      <ExternalLinks section={section} />

      {/* Delete */}
      <DeleteSection section={section} />
    </div>
  );
};

export default SectionMenu;
