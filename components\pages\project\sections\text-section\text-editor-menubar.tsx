import React from "react";
import { Button } from "@/components/ui/button";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Type,
  Heading1,
  Heading2,
  Heading3,
  Strikethrough,
  Code,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link,
  Unlink,
  Highlighter,
  Palette,
  Minus,
  MoreHorizontal,
  PaintBucket,
  AlignVerticalSpaceBetween,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const MenuBar = ({
  editor,
  setIsMenuHovered,
}: {
  editor: any;
  setIsMenuHovered: (hovered: boolean) => void;
}) => {
  if (!editor) {
    return null;
  }

  const MenuButton = ({
    onClick,
    isActive,
    disabled,
    children,
    title,
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      variant="ghost"
      size="sm"
      className={`h-8 w-8 p-0 transition-colors ${
        isActive
          ? "bg-primary text-primary-foreground hover:bg-primary/90"
          : "hover:bg-muted"
      }`}
      onClick={onClick}
      disabled={disabled}
      title={title}
    >
      {children}
    </Button>
  );

  return (
    <div
      className="absolute -top-4 left-1/2 -translate-x-1/2 bg-background border border-border rounded-lg shadow-xl p-2 animate-in fade-in-0 zoom-in-95 duration-200 z-50"
      style={{
        width: "fit-content",
        maxWidth: "min(95vw, 1200px)",
        minWidth: window.innerWidth < 768 ? "320px" : "540px",
      }}
      onMouseEnter={() => setIsMenuHovered(true)}
      onMouseLeave={() => setIsMenuHovered(false)}
    >
      {/* Menu content wrapper with proper overflow handling */}
      <div className="flex items-center gap-0.5 flex-wrap justify-center">
        {/* Text Formatting */}
        <div className="flex items-center gap-0.5">
          {/* Font Size */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 px-2 text-sm transition-colors ${
                  editor.isActive("textStyle", { fontSize: "12px" }) ||
                  editor.isActive("textStyle", { fontSize: "14px" }) ||
                  editor.isActive("textStyle", { fontSize: "16px" }) ||
                  editor.isActive("textStyle", { fontSize: "18px" }) ||
                  editor.isActive("textStyle", { fontSize: "24px" }) ||
                  editor.isActive("textStyle", { fontSize: "32px" })
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-muted"
                }`}
                title="Font Size"
              >
                {editor.isActive("textStyle", { fontSize: "12px" })
                  ? "12px"
                  : editor.isActive("textStyle", { fontSize: "14px" })
                  ? "14px"
                  : editor.isActive("textStyle", { fontSize: "16px" })
                  ? "16px"
                  : editor.isActive("textStyle", { fontSize: "18px" })
                  ? "18px"
                  : editor.isActive("textStyle", { fontSize: "24px" })
                  ? "24px"
                  : editor.isActive("textStyle", { fontSize: "32px" })
                  ? "32px"
                  : "14px"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "12px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "12px" })
                    ? "bg-accent"
                    : ""
                }
              >
                12px - Small
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "14px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "14px" })
                    ? "bg-accent"
                    : ""
                }
              >
                14px - Normal
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "16px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "16px" })
                    ? "bg-accent"
                    : ""
                }
              >
                16px - Medium
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "18px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "18px" })
                    ? "bg-accent"
                    : ""
                }
              >
                18px - Large
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "24px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "24px" })
                    ? "bg-accent"
                    : ""
                }
              >
                24px - Extra Large
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontSize: "32px" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontSize: "32px" })
                    ? "bg-accent"
                    : ""
                }
              >
                32px - Huge
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().unsetFontSize().run()}
              >
                Default Size
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Text Color Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 transition-colors ${
                  editor.isActive("textStyle", { color: "#ff0000" }) ||
                  editor.isActive("textStyle", { color: "#0000ff" }) ||
                  editor.isActive("textStyle", { color: "#008000" })
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-muted"
                }`}
                title="Text Color"
              >
                <Type className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ color: "#ff0000" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { color: "#ff0000" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  Red
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ color: "#0000ff" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { color: "#0000ff" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  Blue
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ color: "#008000" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { color: "#008000" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  Green
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().unsetColor().run()}
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border border-gray-300 rounded"></div>
                  Default
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Background Color */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 transition-colors ${
                  editor.isActive("textStyle", {
                    backgroundColor: "#ffff00",
                  }) ||
                  editor.isActive("textStyle", {
                    backgroundColor: "#ff69b4",
                  }) ||
                  editor.isActive("textStyle", { backgroundColor: "#90ee90" })
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-muted"
                }`}
                title="Background Color (Highlight)"
              >
                <PaintBucket className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ backgroundColor: "#ffff00" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { backgroundColor: "#ffff00" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-400 rounded"></div>
                  Yellow
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ backgroundColor: "#ff69b4" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { backgroundColor: "#ff69b4" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-pink-400 rounded"></div>
                  Pink
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ backgroundColor: "#90ee90" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { backgroundColor: "#90ee90" })
                    ? "bg-accent"
                    : ""
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-300 rounded"></div>
                  Light Green
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().unsetBackgroundColor().run()
                }
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border border-gray-300 rounded"></div>
                  No Highlight
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Font Family */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 px-2 text-sm transition-colors ${
                  editor.isActive("textStyle", { fontFamily: "serif" }) ||
                  editor.isActive("textStyle", { fontFamily: "monospace" }) ||
                  editor.isActive("textStyle", { fontFamily: "cursive" })
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-muted"
                }`}
                title="Font Family"
              >
                Aa
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().unsetFontFamily().run()}
                className={
                  !editor.isActive("textStyle", { fontFamily: "serif" }) &&
                  !editor.isActive("textStyle", { fontFamily: "monospace" }) &&
                  !editor.isActive("textStyle", { fontFamily: "cursive" })
                    ? "bg-accent"
                    : ""
                }
              >
                <span style={{ fontFamily: "sans-serif" }}>
                  Default (Sans-serif)
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontFamily: "serif" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontFamily: "serif" })
                    ? "bg-accent"
                    : ""
                }
              >
                <span style={{ fontFamily: "serif" }}>Serif</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontFamily: "monospace" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontFamily: "monospace" })
                    ? "bg-accent"
                    : ""
                }
              >
                <span style={{ fontFamily: "monospace" }}>Monospace</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ fontFamily: "cursive" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { fontFamily: "cursive" })
                    ? "bg-accent"
                    : ""
                }
              >
                <span style={{ fontFamily: "cursive" }}>Cursive</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <MenuButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            isActive={editor.isActive("bold")}
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            isActive={editor.isActive("italic")}
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleStrike().run()}
            isActive={editor.isActive("strike")}
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleCode().run()}
            isActive={editor.isActive("code")}
            title="Inline Code"
          >
            <Code className="h-4 w-4" />
          </MenuButton>

          {/* Line Height */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 transition-colors ${
                  editor.isActive("textStyle", { lineHeight: "1" }) ||
                  editor.isActive("textStyle", { lineHeight: "1.2" }) ||
                  editor.isActive("textStyle", { lineHeight: "1.5" }) ||
                  editor.isActive("textStyle", { lineHeight: "2" })
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "hover:bg-muted"
                }`}
                title="Line Height"
              >
                <AlignVerticalSpaceBetween className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ lineHeight: "1" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { lineHeight: "1" })
                    ? "bg-accent"
                    : ""
                }
              >
                1.0 - Tight
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ lineHeight: "1.2" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { lineHeight: "1.2" })
                    ? "bg-accent"
                    : ""
                }
              >
                1.2 - Snug
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ lineHeight: "1.5" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { lineHeight: "1.5" })
                    ? "bg-accent"
                    : ""
                }
              >
                1.5 - Normal
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor
                    .chain()
                    .focus()
                    .toggleTextStyle({ lineHeight: "2" })
                    .run()
                }
                className={
                  editor.isActive("textStyle", { lineHeight: "2" })
                    ? "bg-accent"
                    : ""
                }
              >
                2.0 - Loose
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().unsetLineHeight().run()}
              >
                Default
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <MenuButton
            onClick={() => {
              // Underline extension would need to be installed
              if (editor.chain().focus().toggleUnderline) {
                editor.chain().focus().toggleUnderline().run();
              }
            }}
            isActive={editor.isActive("underline")}
            title="Underline (Extension Required)"
            disabled={!editor.chain().focus().toggleUnderline}
          >
            <Underline className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              // Highlight extension would need to be installed
              if (editor.chain().focus().toggleHighlight) {
                editor.chain().focus().toggleHighlight().run();
              }
            }}
            isActive={editor.isActive("highlight")}
            title="Highlight (Extension Required)"
            disabled={!editor.chain().focus().toggleHighlight}
          >
            <Highlighter className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Text Alignment */}
        <div className="flex items-center gap-0.5">
          <MenuButton
            onClick={() => editor.chain().focus().setTextAlign("left").run()}
            isActive={editor.isActive({ textAlign: "left" })}
            title="Align Left (Ctrl+Shift+L)"
          >
            <AlignLeft className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().setTextAlign("center").run()}
            isActive={editor.isActive({ textAlign: "center" })}
            title="Align Center (Ctrl+Shift+E)"
          >
            <AlignCenter className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().setTextAlign("right").run()}
            isActive={editor.isActive({ textAlign: "right" })}
            title="Align Right (Ctrl+Shift+R)"
          >
            <AlignRight className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().setTextAlign("justify").run()}
            isActive={editor.isActive({ textAlign: "justify" })}
            title="Justify Text"
          >
            <AlignJustify className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Headings */}
        <div className="flex items-center gap-0.5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2 text-sm">
                {editor.isActive("heading", { level: 1 })
                  ? "H1"
                  : editor.isActive("heading", { level: 2 })
                  ? "H2"
                  : editor.isActive("heading", { level: 3 })
                  ? "H3"
                  : "P"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().setParagraph().run()}
                className={editor.isActive("paragraph") ? "bg-accent" : ""}
              >
                Paragraph
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 1 }).run()
                }
                className={
                  editor.isActive("heading", { level: 1 }) ? "bg-accent" : ""
                }
              >
                <Heading1 className="h-4 w-4 mr-2" />
                Heading 1
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 2 }).run()
                }
                className={
                  editor.isActive("heading", { level: 2 }) ? "bg-accent" : ""
                }
              >
                <Heading2 className="h-4 w-4 mr-2" />
                Heading 2
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 3 }).run()
                }
                className={
                  editor.isActive("heading", { level: 3 }) ? "bg-accent" : ""
                }
              >
                <Heading3 className="h-4 w-4 mr-2" />
                Heading 3
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Lists */}
        <div className="flex items-center gap-0.5">
          <MenuButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive("bulletList")}
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive("orderedList")}
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            isActive={editor.isActive("blockquote")}
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Links and Special Elements */}
        <div className="flex items-center gap-0.5">
          <MenuButton
            onClick={() => {
              const url = window.prompt("Enter URL:");
              if (url) {
                const setLink = (editor as any).chain().focus().setLink;
                if (setLink) {
                  setLink({ href: url }).run();
                }
              }
            }}
            isActive={editor.isActive("link")}
            title="Add Link (Extension Required)"
            disabled={!(editor as any).chain().focus().setLink}
          >
            <Link className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const unsetLink = (editor as any).chain().focus().unsetLink;
              if (unsetLink) {
                unsetLink().run();
              }
            }}
            disabled={
              !editor.isActive("link") ||
              !(editor as any).chain().focus().unsetLink
            }
            title="Remove Link (Extension Required)"
          >
            <Unlink className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const setHorizontalRule = (editor as any)
                .chain()
                .focus().setHorizontalRule;
              if (setHorizontalRule) {
                setHorizontalRule().run();
              }
            }}
            title="Horizontal Rule (Extension Required)"
            disabled={!(editor as any).chain().focus().setHorizontalRule}
          >
            <Minus className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* More Options */}
        <div className="flex items-center gap-0.5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                title="More Options"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().clearNodes().run()}
              >
                <Palette className="h-4 w-4 mr-2" />
                Clear Formatting
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().selectAll().run()}
              >
                Select All
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  const content = editor.getHTML();
                  navigator.clipboard.writeText(content);
                }}
              >
                Copy HTML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* History */}
        <div className="flex items-center gap-0.5">
          <MenuButton
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            title="Undo (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            title="Redo (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </MenuButton>
        </div>
      </div>
    </div>
  );
};
