// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum SectionType {
  VIDEO
  IMAGE
  TEXT
  TEXTIMAGE
  FORM
  SOCIAL
  MAP
  ECOMMERCE
}

model User {
  id        String    @id @default(uuid())
  name      String
  email     String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  Project   Project[]

  @@map("users")
}

model Project {
  id        String    @id @default(uuid())
  title     String
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  sections  Section[]

  @@index([userId, createdAt(sort: Desc)])
  @@map("projects")
}

model Section {
  id           String        @id @default(uuid())
  title        String?
  projectId    String
  index        Int
  project      Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  type         SectionType
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  text         Text?
  externalLink ExternalLink?

  @@index([projectId, index(sort: Asc)])
  @@map("sections")
}

model Text {
  id           String        @id @default(uuid())
  content      String        @default("<h1> This is you heading </h1> <p> you can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>")
  sectionId    String        @unique @map("sectionId")
  section      Section       @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  externalLink ExternalLink?

  @@index([sectionId])
  @@map("texts")
}

model ExternalLink {
  id        String   @id @default(uuid())
  label     String?  @default("Click Me!")
  url       String   @default("")
  section   Section? @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  sectionId String?  @unique @map("sectionId")
  text      Text?    @relation(fields: [textId], references: [id], onDelete: Cascade)
  textId    String?  @unique @map("textId")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("external_links")
}
