"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import { ExternalLink, Section, Text } from "@prisma/client";
import { useCallback, useState } from "react";
import { MenuBar } from "./text-editor-menubar";
import { Type } from "lucide-react";
import { TextStyleKit } from "@tiptap/extension-text-style";
import { toast } from "sonner";
import { updateTextAction } from "@/actions/text";
import { debounce } from "@/lib/utils2";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import UpdateExternalLinkPopover from "./update-external-link-popover";

type Props = {
  text: Text & { externalLink?: ExternalLink | null };
  sectionId: Section["id"];
};

// Menu Bar Component

export default function TextEditor({
  sectionId,
  text: { id, content, externalLink },
}: Props) {
  const [isEditing, setIsEditing] = useState(false);
  const [isMenuHovered, setIsMenuHovered] = useState(false);

  const debouncedAndUpdate = useCallback(
    debounce(async (id: Text["id"], content: Text["content"]) => {
      const { data, message, error } = await updateTextAction({
        id,
        content,
      });

      if (data) {
        toast.success(message);
      }

      if (error) {
        toast.error(message);
      }
    }),
    []
  );

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      TextStyleKit.configure({
        backgroundColor: {
          types: ["textStyle"],
        },
        color: {
          types: ["textStyle"],
        },
        fontFamily: {
          types: ["textStyle"],
        },
        fontSize: {
          types: ["textStyle"],
        },
        lineHeight: {
          types: ["textStyle"],
        },
      }),
      // Note: Additional extensions like Link, Underline, Highlight, HorizontalRule
      // would need to be installed separately from @tiptap packages
    ],
    content: content || "",
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class:
          "prose prose-sm sm:prose lg:prose-lg xl:prose-2xl focus:outline-none focus:border-none min-h-[200px] p-6 rounded-lg transition-all duration-200 max-w-full!",
      },
      handleKeyDown: (_view, event) => {
        // Custom keyboard shortcuts
        if (event.ctrlKey || event.metaKey) {
          switch (event.key) {
            case "b":
              event.preventDefault();
              editor?.chain().focus().toggleBold().run();
              return true;
            case "i":
              event.preventDefault();
              editor?.chain().focus().toggleItalic().run();
              return true;
            case "u":
              event.preventDefault();
              editor?.chain().focus().toggleUnderline?.().run();
              return true;
            case "`":
              event.preventDefault();
              editor?.chain().focus().toggleCode().run();
              return true;
            case "k":
              event.preventDefault();
              const url = window.prompt("Enter URL:");
              if (url) {
                editor?.chain().focus().setLink?.({ href: url }).run();
              }
              return true;
            case "e":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("center").run();
                return true;
              }
              break;
            case "l":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("left").run();
                return true;
              }
              break;
            case "r":
              if (event.shiftKey) {
                event.preventDefault();
                editor?.chain().focus().setTextAlign("right").run();
                return true;
              }
              break;
            case "\\":
              event.preventDefault();
              editor?.chain().focus().clearNodes().run();
              return true;
          }
        }
        return false;
      },
    },
    onFocus: () => setIsEditing(true),
    onBlur: () => {
      // Delay blur to allow menu interactions, but check if menu is hovered
      setTimeout(() => {
        if (!isMenuHovered) {
          setIsEditing(false);
        }
      }, 200);
    },
    onCreate: async ({ editor }) => {
      // Auto-save functionality can be added here
      console.log("Editor created:", editor);
    },
    onUpdate: async ({ editor }) => {
      // Auto-save functionality can be added here
      debouncedAndUpdate(id, editor.getHTML());
    },
  });

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-muted rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div
      className="group/editor relative"
      style={{
        paddingTop: isEditing ? "5rem" : "0",
        transition: "padding-top 200ms ease",
      }}
    >
      {/* Floating menu bar - only visible when editing */}
      {isEditing && (
        <MenuBar editor={editor} setIsMenuHovered={setIsMenuHovered} />
      )}

      {/* Editor content */}
      <div
        className={`relative transition-all duration-200 ${
          isEditing
            ? "ring-2 ring-primary/20 bg-background"
            : "hover:bg-muted/20"
        } rounded-lg`}
      >
        {!isEditing && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/editor:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-background/90 backdrop-blur-sm border border-border rounded-lg px-3 py-1 flex items-center gap-2 text-sm text-muted-foreground">
              <Type className="h-4 w-4" />
              Click to edit text
            </div>
          </div>
        )}
        <EditorContent editor={editor} />
        {externalLink && (
          <UpdateExternalLinkPopover externalLink={externalLink}>
            <Button>{externalLink?.label}</Button>
          </UpdateExternalLinkPopover>
        )}
      </div>
    </div>
  );
}
