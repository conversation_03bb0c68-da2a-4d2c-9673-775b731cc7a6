import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Ellipsis, Trash2Icon } from "lucide-react";
import DeleteDialog from "../../core/delete-dialog";
import { deleteProjectAction } from "@/actions/projects";
import { Project } from "@prisma/client";

type Props = {
  id: Project["id"];
  title: string;
};

const ProjectDropdownMenu = ({ id }: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-3 right-3 z-10 h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background opacity-0 group-hover:opacity-100 transition-opacity duration-200"
        >
          <Ellipsis className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem asChild>
          <DeleteDialog action={deleteProjectAction.bind(null, id)}>
            <Button
              variant="ghost"
              className="w-full justify-start gap-2 text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <Trash2Icon className="h-4 w-4" />
              <span>Delete Project</span>
            </Button>
          </DeleteDialog>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProjectDropdownMenu;
