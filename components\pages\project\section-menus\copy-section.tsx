import React from "react";
import { Section } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Copy } from "lucide-react";

type Props = {
  section: Section;
};

const CopySection = (props: Props) => {
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Duplicate section"
      >
        <Copy className="h-4 w-4" />
      </Button>
    </>
  );
};

export default CopySection;
