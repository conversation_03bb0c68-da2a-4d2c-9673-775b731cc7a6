"use server";
import db from "@/lib/db";

export const createProject = async (title: string, userId: string) => {
  const project = await db.project.create({
    data: {
      title,
      userId,
    },
  });

  return project;
};

export const updateProjectTitle = async (title: string, id: string) => {
  const project = await db.project.update({
    where: {
      id,
    },
    data: {
      title,
    },
  });

  return project;
};

export const isProject = async (id: string) => {
  const project = await db.project.findUnique({
    where: {
      id,
    },
  });

  return project;
};

export const deleteProject = async (id: string) => {
  const project = await db.project.delete({
    where: {
      id,
    },
  });

  return project;
};
