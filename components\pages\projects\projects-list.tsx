import React from "react";
import ProjectItem from "./project-item";
import { Project } from "@prisma/client";
import { Plus, Rocket } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ProjectCreateDialog from "./project-create-dialog";

type Props = {
  projects: Project[];
};

const ProjectsList = ({ projects }: Props) => {
  if (projects.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
            <Rocket className="w-12 h-12 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-3">
            Ready to Launch?
          </h3>
          <p className="text-muted-foreground mb-8 leading-relaxed">
            You haven't created any projects yet. Start building your first
            website and bring your vision to life!
          </p>
          <ProjectCreateDialog />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-foreground">
            Your Projects
          </h2>
          <p className="text-muted-foreground">
            {projects.length} project{projects.length !== 1 ? "s" : ""} ready to
            edit
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 xl:grid-cols-3">
        {projects.map((project) => (
          <ProjectItem key={project.id} project={project} />
        ))}

        {/* Add new project card */}
        <ProjectCreateDialog>
          <Card className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 border-dashed border-border/50 hover:border-primary/50 bg-muted/20 hover:bg-muted/40">
            <CardContent className="flex flex-col items-center justify-center p-8 text-center min-h-[200px]">
              <div className="w-16 h-16 mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <Plus className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                Create New Project
              </h3>
              <p className="text-sm text-muted-foreground">
                Start building your next amazing website
              </p>
            </CardContent>
          </Card>
        </ProjectCreateDialog>
      </div>
    </div>
  );
};

export default ProjectsList;
