"use client";

import React from "react";
import {
  CameraIcon,
  Image,
  ImagesIcon,
  LetterText,
  MapPin,
  ShoppingCart,
  Users,
  Mail,
} from "lucide-react";
import { createSectionAction } from "@/actions/section";
import { $Enums, Section } from "@prisma/client";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptions = ({ index, projectId }: Props) => {
  const options = [
    {
      id: 1,
      type: $Enums.SectionType.TEXT,
      icon: LetterText,
      label: "Text",
      description: "Add headings, paragraphs, and rich text content",
      iconColor: "text-blue-600 dark:text-blue-400",
      bgColor:
        "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",
    },
    {
      id: 2,
      type: $Enums.SectionType.IMAGE,
      icon: Image,
      label: "Image",
      description: "Display beautiful images and galleries",
      iconColor: "text-green-600 dark:text-green-400",
      bgColor:
        "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
    },
    {
      id: 3,
      type: $Enums.SectionType.VIDEO,
      icon: CameraIcon,
      label: "Video",
      description: "Embed videos and multimedia content",
      iconColor: "text-purple-600 dark:text-purple-400",
      bgColor:
        "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
    },
    {
      id: 4,
      type: $Enums.SectionType.TEXTIMAGE,
      icon: ImagesIcon,
      label: "Text + Image",
      description: "Combine text with images for rich layouts",
      iconColor: "text-orange-600 dark:text-orange-400",
      bgColor:
        "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20",
    },
    {
      id: 5,
      type: $Enums.SectionType.FORM,
      icon: Mail,
      label: "Form",
      description: "Contact forms and data collection",
      iconColor: "text-pink-600 dark:text-pink-400",
      bgColor:
        "from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20",
    },
    {
      id: 6,
      type: $Enums.SectionType.SOCIAL,
      icon: Users,
      label: "Social",
      description: "Social media feeds and sharing buttons",
      iconColor: "text-indigo-600 dark:text-indigo-400",
      bgColor:
        "from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20",
    },
    {
      id: 7,
      type: $Enums.SectionType.MAP,
      icon: MapPin,
      label: "Map",
      description: "Interactive maps and location displays",
      iconColor: "text-teal-600 dark:text-teal-400",
      bgColor:
        "from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20",
    },
    {
      id: 8,
      type: $Enums.SectionType.ECOMMERCE,
      icon: ShoppingCart,
      label: "E-commerce",
      description: "Product showcases and shopping features",
      iconColor: "text-red-600 dark:text-red-400",
      bgColor: "from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20",
    },
  ];

  const handleSectionCreate = async (type: $Enums.SectionType) => {
    const { data, message, error } = await createSectionAction({
      projectId: projectId,
      index: index,
      type,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <div className="py-2">
      <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-4 overflow-hidden">
        {options.map((option) => (
          <Card
            key={option.type}
            className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-border/50 hover:border-border"
            onClick={() => handleSectionCreate(option.type)}
          >
            <CardContent className="p-2 text-center">
              {/* Icon with gradient background */}
              <div
                className={`w-8 h-8 mx-auto mb-3 bg-gradient-to-br ${option.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}
              >
                <option.icon
                  className={`w-6 h-6 ${option.iconColor} group-hover:scale-110 transition-all duration-200`}
                />
              </div>

              {/* Label */}
              <h3 className="font-semibold text-sm text-foreground mb-1 group-hover:text-primary transition-colors">
                {option.label}
              </h3>

              {/* Description */}
              <p className="text-xs text-muted-foreground leading-relaxed">
                {option.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SectionOptions;
