import React from "react";
import Container from "@/components/core/container";
import LazySection from "@/components/core/lazy-section";
import HeroSection from "./landing-page/hero-section";
import FeaturesSection from "./landing-page/features-section";
import SocialProofSection from "./landing-page/social-proof-section";
import SecondaryCTASection from "./landing-page/secondary-cta-section";
import Footer from "./landing-page/footer";

// Type definitions for landing page sections
export interface LandingPageProps {
  className?: string;
}

export interface HeroContent {
  headline: string;
  subheading: string;
  ctaText: string;
  ctaHref: string;
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  avatar?: string;
}

export interface Statistic {
  id: string;
  value: string;
  label: string;
  description?: string;
}

export interface SocialProofContent {
  testimonials: Testimonial[];
  statistics: Statistic[];
}

export interface SecondaryCTAContent {
  headline: string;
  description: string;
  ctaText: string;
  ctaHref: string;
}

export interface FooterLink {
  id: string;
  label: string;
  href: string;
}

export interface FooterSection {
  id: string;
  title: string;
  links: FooterLink[];
}

export interface FooterContent {
  sections: FooterSection[];
  contactInfo: {
    email: string;
    phone?: string;
    address?: string;
  };
  socialLinks: {
    platform: string;
    href: string;
    icon: string;
  }[];
  copyright: string;
}

const LandingPage: React.FC<LandingPageProps> = ({ className }) => {
  return (
    <main className={className}>
      <HeroSection />
      <LazySection>
        <FeaturesSection />
      </LazySection>
      <LazySection>
        <SocialProofSection />
      </LazySection>
      <LazySection>
        <SecondaryCTASection />
      </LazySection>
      <LazySection>
        <Footer />
      </LazySection>
    </main>
  );
};

export default LandingPage;
