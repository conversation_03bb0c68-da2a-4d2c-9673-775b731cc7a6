-- CreateTable
CREATE TABLE "external_links" (
    "id" TEXT NOT NULL,
    "label" TEXT DEFAULT 'Click Me!',
    "url" TEXT NOT NULL DEFAULT '',
    "sectionId" TEXT,
    "textId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "external_links_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "external_links_sectionId_key" ON "external_links"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "external_links_textId_key" ON "external_links"("textId");

-- CreateIndex
CREATE INDEX "texts_sectionId_idx" ON "texts"("sectionId");

-- AddForeignKey
ALTER TABLE "external_links" ADD CONSTRAINT "external_links_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "sections"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "external_links" ADD CONSTRAINT "external_links_textId_fkey" FOREIGN KEY ("textId") REFERENCES "texts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
