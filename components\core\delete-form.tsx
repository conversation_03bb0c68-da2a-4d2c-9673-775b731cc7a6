import Form from "next/form";
import React from "react";
import { Button } from "../ui/button";
import { Trash2 } from "lucide-react";
import { toast } from "sonner";
import { ActionState } from "@/lib/types";

type Props<T> = {
  action: () => Promise<ActionState<T>>;
};

export default function DeleteForm<T>({ action }: Props<T>) {
  const formAction = async (formData: FormData) => {
    const { data, message, error } = await action();

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };
  return (
    <Form action={formAction} className="w-full">
      <Button variant="destructive">
        <Trash2 className="size-4" />
        <span>Delete</span>
      </Button>
    </Form>
  );
}
