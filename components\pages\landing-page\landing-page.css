/* Landing Page Performance Optimizations */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-bounce,
  .animate-pulse,
  .group-hover\:translate-x-1,
  .hover\:-translate-y-1,
  .transition-transform,
  .transition-all {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
}

/* Optimize animations for better performance */
.landing-hero-bg {
  will-change: opacity;
  transform: translateZ(0);
}

.landing-feature-card {
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.landing-cta-button {
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Focus visible improvements */
.landing-focus-ring:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background: hsl(var(--primary)) !important;
  }
  
  .text-transparent {
    color: hsl(var(--foreground)) !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
  }
}

/* Print styles */
@media print {
  .animate-bounce,
  .animate-pulse,
  .landing-hero-bg,
  .absolute {
    display: none !important;
  }
  
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background: none !important;
    color: black !important;
  }
}

/* Loading state optimizations */
.landing-loading-skeleton {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted-foreground) / 0.1) 50%, 
    hsl(var(--muted)) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Intersection observer fade-in */
.landing-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.landing-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* GPU acceleration for smooth animations */
.landing-gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize text rendering */
.landing-text-optimized {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
