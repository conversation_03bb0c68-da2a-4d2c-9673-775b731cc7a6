/*
  Warnings:

  - You are about to drop the `TEXT` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "TEXT" DROP CONSTRAINT "TEXT_sectionId_fkey";

-- DropTable
DROP TABLE "TEXT";

-- CreateTable
CREATE TABLE "Text" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL DEFAULT '<h1> This is you heading </h1> <p> ypu can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>',
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Text_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Text_sectionId_key" ON "Text"("sectionId");

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE "Text" ADD CONSTRAINT "Text_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "Section"("id") ON DELETE CASCADE ON UPDATE CASCADE;
