"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import ProjectCreateForm from "./project-create-form";

type Props = {};

const ProjectCreateDialog = (props: Props) => {
  return (
    <>
      <Dialog>
        <DialogTrigger asChild>
          <Button>Create New Project</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle> Create New Project </DialogTitle>
            <DialogDescription>
              This is the description of the dialog.
            </DialogDescription>
          </DialogHeader>
          <ProjectCreateForm />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProjectCreateDialog;
