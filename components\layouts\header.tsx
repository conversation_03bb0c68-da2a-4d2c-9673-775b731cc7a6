"use client";
import React from "react";
import Logo from "./logo";
import Container from "../core/container";
import AuthButtons from "./auth-buttons";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { ArrowLeft, Eye, Settings, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type Props = {};

const Header = (props: Props) => {
  const pathname = usePathname();
  const isProjectPath = pathname.includes("/project/");
  const isLandingPage = pathname === "/";

  return (
    <>
      {isProjectPath ? (
        <header className="dark sticky z-40 bg-background/80 backdrop-blur-md border-b border-border/50">
          <div className="container mx-auto px-8 py-4">
            <div className="flex items-center justify-between">
              {/* Left side - Back button and title */}
              <div className="flex items-center gap-4">
                <Link href="/project">
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 text-muted-foreground hover:text-foreground"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Projects
                  </Button>
                </Link>
                <div className="h-6 w-px bg-border" />
                <div>
                  <h1 className="text-xl font-bold text-foreground">
                    Website Builder
                  </h1>
                  <p className="text-sm text-muted-foreground">
                    Design your perfect website
                  </p>
                </div>
              </div>

              {/* Right side - Action buttons */}
              <div className="flex items-center gap-2">
                <Button variant="primary" size="sm" className="gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </Button>
                <Button variant="primary" size="sm" className="gap-2 ">
                  <Settings className="h-4 w-4" />
                  Settings
                </Button>
                <Button
                  size="sm"
                  className="gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 dark:text-white"
                >
                  <Share2 className="h-4 w-4" />
                  Publish
                </Button>
              </div>
            </div>
          </div>
        </header>
      ) : (
        <header className={cn("sticky top-0 z-50 bg-background")}>
          <Container className="flex justify-between items-center p-4">
            <Logo />
            <AuthButtons isVisible={isLandingPage} />
          </Container>
        </header>
      )}
    </>
  );
};

export default Header;
