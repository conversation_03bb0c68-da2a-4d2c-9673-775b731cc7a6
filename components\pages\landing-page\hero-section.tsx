"use client";

import React from "react";
import Container from "@/components/core/container";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Zap } from "lucide-react";
import "./landing-page.css";

export interface HeroSectionProps {
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  return (
    <section
      className={`relative min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 ${
        className || ""
      }`}
      aria-labelledby="hero-heading"
      role="banner"
    >
      {/* Background decorative elements */}
      <div
        className="absolute inset-0 overflow-hidden landing-hero-bg"
        aria-hidden="true"
      >
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-pulse landing-gpu-accelerated" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000 landing-gpu-accelerated" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-muted/5 rounded-full blur-3xl landing-gpu-accelerated" />
      </div>

      <Container className="relative z-10 text-center">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Badge */}
          <div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary border border-primary/20"
            role="banner"
          >
            <Sparkles className="w-4 h-4" aria-hidden="true" />
            <span className="text-sm font-medium">No-Code Website Builder</span>
          </div>

          {/* Main Headline */}
          <h1
            id="hero-heading"
            className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight landing-text-optimized"
          >
            <span className="bg-gradient-to-r from-foreground via-foreground to-foreground/80 bg-clip-text text-transparent">
              Build Stunning Websites
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary via-primary to-primary/80 bg-clip-text text-transparent">
              Without Code
            </span>
          </h1>

          {/* Subheading */}
          <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Create professional, responsive websites in minutes with Viber's
            intuitive drag-and-drop builder. No coding skills required – just
            your creativity.
          </p>

          {/* CTA Buttons */}
          <div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4"
            role="group"
            aria-label="Call to action buttons"
          >
            <Button
              asChild
              size="lg"
              className="text-lg px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group focus:ring-4 focus:ring-primary/20"
            >
              <Link
                href="/project"
                className="flex items-center gap-2"
                aria-label="Start building your website for free"
              >
                Start Building Free
                <ArrowRight
                  className="w-5 h-5 group-hover:translate-x-1 transition-transform"
                  aria-hidden="true"
                />
              </Link>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 rounded-xl border-2 hover:bg-muted/50 transition-all duration-300 focus:ring-4 focus:ring-primary/20"
            >
              <Link
                href="#features"
                className="flex items-center gap-2"
                aria-label="View website builder features"
              >
                <Zap className="w-5 h-5" aria-hidden="true" />
                See Features
              </Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="pt-12 space-y-4">
            <p className="text-sm text-muted-foreground">
              Trusted by thousands of creators worldwide
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              <div className="text-2xl font-bold">10K+</div>
              <div className="w-px h-6 bg-border" />
              <div className="text-2xl font-bold">Websites</div>
              <div className="w-px h-6 bg-border" />
              <div className="text-2xl font-bold">50+</div>
              <div className="w-px h-6 bg-border" />
              <div className="text-2xl font-bold">Countries</div>
            </div>
          </div>
        </div>
      </Container>

      {/* Scroll indicator */}
      <div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
        aria-hidden="true"
      >
        <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-muted-foreground/30 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
