"use client";

import React from "react";
import Container from "@/components/core/container";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { ArrowR<PERSON>, CheckCircle, Spark<PERSON> } from "lucide-react";

export interface SecondaryCTASectionProps {
  className?: string;
}

const benefits = [
  "No credit card required",
  "Free SSL certificate included",
  "24/7 customer support",
  "30-day money-back guarantee",
  "Unlimited bandwidth",
  "Professional templates included"
];

const SecondaryCTASection: React.FC<SecondaryCTASectionProps> = ({ className }) => {
  return (
    <section className={`py-24 bg-gradient-to-br from-primary/5 via-background to-muted/10 ${className || ""}`}>
      <Container>
        <div className="max-w-4xl mx-auto">
          <Card className="border-2 border-primary/20 bg-gradient-to-br from-background to-primary/5 shadow-2xl">
            <CardContent className="p-8 md:p-12">
              <div className="text-center space-y-6">
                {/* Badge */}
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 text-primary border border-primary/20">
                  <Sparkles className="w-4 h-4" />
                  <span className="text-sm font-medium">Limited Time Offer</span>
                </div>

                {/* Headline */}
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight">
                  Ready to Build Your
                  <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"> Dream Website?</span>
                </h2>

                {/* Description */}
                <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                  Join thousands of creators who have already transformed their ideas into stunning websites. 
                  Start your journey today with our free plan.
                </p>

                {/* Benefits Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-8 max-w-2xl mx-auto">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3 text-left">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm md:text-base text-muted-foreground">{benefit}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
                  <Button 
                    asChild 
                    size="lg" 
                    className="text-lg px-8 py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
                  >
                    <Link href="/project" className="flex items-center gap-2">
                      Get Started Free
                      <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-lg px-8 py-6 rounded-xl border-2 border-primary/20 hover:bg-primary/5 transition-all duration-300"
                  >
                    <Link href="#features">
                      View Pricing
                    </Link>
                  </Button>
                </div>

                {/* Trust indicator */}
                <div className="pt-6">
                  <p className="text-sm text-muted-foreground">
                    🚀 Over 1,000 websites launched this month
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional value proposition */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">5 min</div>
              <div className="text-sm text-muted-foreground">Average setup time</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">0%</div>
              <div className="text-sm text-muted-foreground">Coding required</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-primary">100%</div>
              <div className="text-sm text-muted-foreground">Satisfaction guaranteed</div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SecondaryCTASection;
