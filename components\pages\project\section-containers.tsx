import React from "react";
import Container from "@/components/core/container";
import SectionMenu from "./section-menu";
import { ExternalLink, Section, Text } from "@prisma/client";

type Props = {
  children: React.ReactNode;
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
  };
};

const SectionContainers = ({ children, section }: Props) => {
  return (
    <section
      className="relative group/section bg-white dark:bg-slate-800/50 border border-border/50 rounded-lg my-4 transition-all duration-300 hover:shadow-lg hover:border-border"
      style={{ overflow: "visible" }}
    >
      {/* Section overlay for better visual feedback */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover/section:opacity-100 transition-opacity duration-300 pointer-events-none" />

      {/* Section menu */}

      <SectionMenu section={section} />

      {/* Section content */}
      <Container>{children}</Container>

      {/* Section index indicator */}
      <div className="absolute top-4 left-4 opacity-0 group-hover/section:opacity-100 transition-opacity duration-200">
        <div className="bg-muted text-muted-foreground text-xs px-2 py-1 rounded-md font-medium">
          Section {section.index + 1}
        </div>
      </div>
    </section>
  );
};

export default SectionContainers;
