-- CreateEnum
CREATE TYPE "SectionType" AS ENUM ('VIDEO', 'IMAGE', 'TEXT', 'TEXTIMAGE', 'FORM', 'SOCIAL', 'MAP', 'ECOMMERCE');

-- CreateTable
CREATE TABLE "Section" (
    "id" TEXT NOT NULL,
    "title" TEXT,
    "projectId" TEXT NOT NULL,
    "index" INTEGER NOT NULL,
    "type" "SectionType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Section_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TEXT" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL DEFAULT '<h1> This is you heading </h1> <p> ypu can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>',
    "sectionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TEXT_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Section_index_key" ON "Section"("index");

-- CreateIndex
CREATE INDEX "Section_projectId_index_idx" ON "Section"("projectId", "index" ASC);

-- CreateIndex
CREATE UNIQUE INDEX "TEXT_sectionId_key" ON "TEXT"("sectionId");

-- AddForeignKey
ALTER TABLE "Section" ADD CONSTRAINT "Section_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TEXT" ADD CONSTRAINT "TEXT_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "Section"("id") ON DELETE CASCADE ON UPDATE CASCADE;
