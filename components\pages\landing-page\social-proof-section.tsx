"use client";

import React from "react";
import Container from "@/components/core/container";
import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote, TrendingUp, Users, Globe, Zap } from "lucide-react";

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
}

export interface Statistic {
  id: string;
  value: string;
  label: string;
  description: string;
  icon: React.ReactNode;
}

export interface SocialProofSectionProps {
  className?: string;
}

const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Small Business Owner",
    company: "Bloom Bakery",
    content: "<PERSON><PERSON> made it incredibly easy to create a professional website for my bakery. The drag-and-drop interface is so intuitive, and I had my site live in just a few hours!",
    rating: 5,
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "Freelance Designer",
    company: "Chen Creative",
    content: "As a designer, I appreciate the flexibility and clean code that <PERSON><PERSON> generates. It's perfect for quickly prototyping ideas and delivering client projects.",
    rating: 5,
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "Marketing Manager",
    company: "TechStart Inc.",
    content: "We needed to launch landing pages quickly for our campaigns. Viber's templates and customization options helped us create high-converting pages in record time.",
    rating: 5,
  },
];

const statistics: Statistic[] = [
  {
    id: "websites",
    value: "50K+",
    label: "Websites Created",
    description: "Trusted by creators worldwide",
    icon: <Globe className="w-6 h-6" />,
  },
  {
    id: "users",
    value: "25K+",
    label: "Happy Users",
    description: "Growing community of builders",
    icon: <Users className="w-6 h-6" />,
  },
  {
    id: "performance",
    value: "99.9%",
    label: "Uptime",
    description: "Reliable hosting infrastructure",
    icon: <TrendingUp className="w-6 h-6" />,
  },
  {
    id: "speed",
    value: "<2s",
    label: "Load Time",
    description: "Lightning-fast performance",
    icon: <Zap className="w-6 h-6" />,
  },
];

const SocialProofSection: React.FC<SocialProofSectionProps> = ({ className }) => {
  return (
    <section className={`py-24 bg-background ${className || ""}`}>
      <Container>
        {/* Statistics */}
        <div className="mb-20">
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
              Trusted by Thousands of
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent"> Creators</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Join a growing community of entrepreneurs, designers, and businesses who trust Viber to power their online presence.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {statistics.map((stat) => (
              <Card key={stat.id} className="text-center border-0 bg-muted/30 hover:bg-muted/50 transition-colors duration-300">
                <CardContent className="pt-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-primary/10 text-primary mb-4">
                    {stat.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-foreground mb-2">{stat.value}</div>
                  <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                  <div className="text-xs text-muted-foreground">{stat.description}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div>
          <div className="text-center space-y-4 mb-12">
            <h3 className="text-2xl md:text-3xl font-bold tracking-tight">What Our Users Say</h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Don't just take our word for it. Here's what real users have to say about their experience with Viber.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  
                  <div className="relative mb-6">
                    <Quote className="absolute -top-2 -left-2 w-6 h-6 text-primary/20" />
                    <p className="text-muted-foreground leading-relaxed pl-4">
                      "{testimonial.content}"
                    </p>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                      <span className="text-sm font-semibold text-primary">
                        {testimonial.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-sm">{testimonial.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {testimonial.role} at {testimonial.company}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SocialProofSection;
