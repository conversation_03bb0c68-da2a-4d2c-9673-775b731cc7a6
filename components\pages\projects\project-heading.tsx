import React from "react";
import ProjectCreateDialog from "./project-create-dialog";
import { Sparkles, FolderOpen } from "lucide-react";

type Props = {};

const ProjectHeading = ({}: Props) => {
  return (
    <div className="text-center space-y-6">
      {/* Hero section */}
      <div className="space-y-4">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <FolderOpen className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Your Projects
          </h1>
          <Sparkles className="w-8 h-8 text-yellow-500 animate-pulse" />
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Create stunning websites without coding. Manage all your projects in
          one place and bring your ideas to life.
        </p>
      </div>

      {/* Action section */}
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
        <ProjectCreateDialog />
        <div className="text-sm text-muted-foreground">
          or browse your existing projects below
        </div>
      </div>
    </div>
  );
};

export default ProjectHeading;
