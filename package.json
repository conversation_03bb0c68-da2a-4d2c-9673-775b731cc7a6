{"name": "wb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4000", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate"}, "prisma": {"seed": "tsx prisma/seed.ts", "schema": "prisma/schema.prisma"}, "dependencies": {"@clerk/nextjs": "^6.25.4", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "prisma": "^6.12.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}