import React from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink, Section, Text } from "@prisma/client";
import { Link } from "lucide-react";
import { toast } from "sonner";
import {
  createExternalSectionLinkAction,
  createExternalTextLinkAction,
} from "@/actions/external-link";

type Props = {
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
  };
};

const ExternalLinks = ({ section }: Props) => {
  const handleExternalLink = async () => {
    if (!section?.text?.id) {
      return toast.error("No text section found");
    }

    const { data, message, error } = await createExternalTextLinkAction({
      textId: section.text.id,
    });

    if (error) {
      return toast.error(error.message);
    }

    if (!data) {
      return toast.error(message);
    }

    toast.success(message);
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="External Link section"
        onClick={handleExternalLink}
      >
        <Link className="h-4 w-4" />
      </Button>
    </>
  );
};

export default ExternalLinks;
