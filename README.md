# Viber

Viber is a web builder that allows you to create a website without coding. It is built with Next.js, TypeScript, and Tailwind CSS.

## Features

- Create a website without coding
- Drag and drop sections
- Customize sections
- Publish your website
- Manage your website
- Manage your website's content
- Manage your website's design
- Manage your website's analytics
- Manage your website's users
- Manage your website's security
- Manage your website's payments
- Manage your website's SEO
- Manage your website's social media
- Manage your website's email
- Manage your website's forms
- Manage your website's maps
- Manage your website's e-commerce
- Manage your website's video
- Manage your website's image
- Manage your website's text
- Manage your website's text image
- Manage your website's social
- Manage your website's map
- Manage your website's e-commerce

## Getting Started

1. Clone the repository
2. Install dependencies with `bun install`
3. Create a `.env.local` file and add your environment variables
4. Run the development server with `bun run dev`
5. Open [http://localhost:4000](http://localhost:4000) with your browser to see the result

## Environment Variables

- `DATABASE_URL` - PostgreSQL database URL
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk publishable key
- `CLERK_SECRET_KEY` - Clerk secret key
- `NEXT_PUBLIC_CLERK_SIGN_IN_URL` - Clerk sign in URL
- `NEXT_PUBLIC_CLERK_SIGN_UP_URL` - Clerk sign up URL
- `NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL` - Clerk after sign in URL

## License

MIT License
