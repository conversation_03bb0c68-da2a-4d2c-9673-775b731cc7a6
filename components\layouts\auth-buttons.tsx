import React from "react";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";
type Props = {
  isVisible: boolean;
};

const AuthButtons = ({ isVisible }: Props) => {
  return (
    <div className="flex gap-4">
      <SignedOut>
        <SignInButton>
          <Button variant="outline">Sign In</Button>
        </SignInButton>
        <SignUpButton>
          <Button>Sign Up</Button>
        </SignUpButton>
      </SignedOut>
      <SignedIn>
        {isVisible && (
          <Button asChild>
            <Link href="/project">Launch</Link>
          </Button>
        )}
        <UserButton />
      </SignedIn>
    </div>
  );
};

export default AuthButtons;
