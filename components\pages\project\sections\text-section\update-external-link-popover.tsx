import React, { useState } from "react";
import {
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { updateExternalLinkAction } from "@/actions/external-link";
import { ExternalLink } from "@prisma/client";
import Form from "next/form";
import { toast } from "sonner";

type Props = {
  children: React.ReactNode;
  externalLink: ExternalLink;
};

const UpdateExternalLinkPopover = ({ children, externalLink }: Props) => {
  const [label, setLabel] = useState(externalLink.label);
  const [url, setUrl] = useState(externalLink.url);

  const handleUpdateExternalLink = async (formData: FormData) => {
    const { data, message, error } = await updateExternalLinkAction({
      id: externalLink.id,
      label,
      url,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>{children}</PopoverTrigger>
        <PopoverContent align="start" className="w-96">
          <div className="grid gap-4">
            <div>
              <h3 className="font-semibold text-foreground">Update Link</h3>
              <hr className="border-border" />
              <p className="text-muted-foreground text-sm">
                Update the label and URL for the external link.
              </p>
            </div>

            <Form className="grid gap-2" action={handleUpdateExternalLink}>
              <div className="space-y-2">
                <Label htmlFor="name">Label</Label>
                <Input
                  placeholder="Check Out"
                  type="text"
                  id="name"
                  value="Label"
                  defaultValue={externalLink.url}
                  onChange={(e) => setLabel(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">URL</Label>
                <Input
                  placeholder="http://example.com"
                  id="name"
                  value="URL"
                  defaultValue={externalLink.url}
                  onChange={(e) => setUrl(e.target.value)}
                />
              </div>
              <Button>Update</Button>
            </Form>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default UpdateExternalLinkPopover;
