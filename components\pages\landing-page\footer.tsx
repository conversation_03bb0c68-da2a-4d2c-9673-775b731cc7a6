"use client";

import React from "react";
import Container from "@/components/core/container";
import Link from "next/link";
import Image from "next/image";
import myLogo from "@/public/logo.png";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Twitter, 
  Facebook, 
  Instagram, 
  Linkedin,
  Github,
  Heart
} from "lucide-react";

export interface FooterLink {
  id: string;
  label: string;
  href: string;
}

export interface FooterSection {
  id: string;
  title: string;
  links: FooterLink[];
}

export interface FooterProps {
  className?: string;
}

const footerSections: FooterSection[] = [
  {
    id: "product",
    title: "Product",
    links: [
      { id: "features", label: "Features", href: "#features" },
      { id: "templates", label: "Templates", href: "/templates" },
      { id: "pricing", label: "Pricing", href: "/pricing" },
      { id: "integrations", label: "Integrations", href: "/integrations" },
    ],
  },
  {
    id: "company",
    title: "Company",
    links: [
      { id: "about", label: "About Us", href: "/about" },
      { id: "careers", label: "Careers", href: "/careers" },
      { id: "blog", label: "Blog", href: "/blog" },
      { id: "press", label: "Press", href: "/press" },
    ],
  },
  {
    id: "support",
    title: "Support",
    links: [
      { id: "help", label: "Help Center", href: "/help" },
      { id: "contact", label: "Contact Us", href: "/contact" },
      { id: "status", label: "Status", href: "/status" },
      { id: "community", label: "Community", href: "/community" },
    ],
  },
  {
    id: "legal",
    title: "Legal",
    links: [
      { id: "privacy", label: "Privacy Policy", href: "/privacy" },
      { id: "terms", label: "Terms of Service", href: "/terms" },
      { id: "cookies", label: "Cookie Policy", href: "/cookies" },
      { id: "gdpr", label: "GDPR", href: "/gdpr" },
    ],
  },
];

const socialLinks = [
  { platform: "Twitter", href: "https://twitter.com/viber", icon: <Twitter className="w-5 h-5" /> },
  { platform: "Facebook", href: "https://facebook.com/viber", icon: <Facebook className="w-5 h-5" /> },
  { platform: "Instagram", href: "https://instagram.com/viber", icon: <Instagram className="w-5 h-5" /> },
  { platform: "LinkedIn", href: "https://linkedin.com/company/viber", icon: <Linkedin className="w-5 h-5" /> },
  { platform: "GitHub", href: "https://github.com/viber", icon: <Github className="w-5 h-5" /> },
];

const Footer: React.FC<FooterProps> = ({ className }) => {
  return (
    <footer className={`bg-muted/30 border-t ${className || ""}`}>
      <Container>
        <div className="py-16">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 mb-12">
            {/* Brand Section */}
            <div className="lg:col-span-2 space-y-6">
              <Link href="/" className="inline-block">
                <Image src={myLogo} alt="Viber" className="h-12 w-auto" />
              </Link>
              
              <p className="text-muted-foreground max-w-md leading-relaxed">
                Viber is the easiest way to create stunning websites without any coding knowledge. 
                Build, customize, and launch your dream website in minutes.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <Phone className="w-4 h-4" />
                  <span>+****************</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  <span>San Francisco, CA</span>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex gap-4">
                {socialLinks.map((social) => (
                  <Link
                    key={social.platform}
                    href={social.href}
                    className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-background border hover:bg-primary hover:text-primary-foreground transition-colors duration-300"
                    aria-label={social.platform}
                  >
                    {social.icon}
                  </Link>
                ))}
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section) => (
              <div key={section.id} className="space-y-4">
                <h3 className="font-semibold text-foreground">{section.title}</h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.id}>
                      <Link
                        href={link.href}
                        className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Newsletter Signup */}
          <div className="border-t border-border pt-8 mb-8">
            <div className="max-w-md">
              <h3 className="font-semibold text-foreground mb-2">Stay Updated</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Get the latest updates, tips, and exclusive offers delivered to your inbox.
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-3 py-2 text-sm border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                />
                <button className="px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors duration-200">
                  Subscribe
                </button>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-border pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-muted-foreground">
              © 2024 Viber. All rights reserved.
            </div>
            
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              Made with <Heart className="w-4 h-4 text-red-500 fill-current" /> by the Viber team
            </div>
          </div>
        </div>
      </Container>
    </footer>
  );
};

export default Footer;
