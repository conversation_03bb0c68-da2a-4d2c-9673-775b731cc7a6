import React from "react";
import { Section } from "@prisma/client";
import { But<PERSON> } from "@/components/ui/button";
import { Settings } from "lucide-react";

type Props = {
  section: Section;
};

const SectionSettings = (props: Props) => {
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Section settings"
      >
        <Settings className="h-4 w-4" />
      </Button>
    </>
  );
};

export default SectionSettings;
