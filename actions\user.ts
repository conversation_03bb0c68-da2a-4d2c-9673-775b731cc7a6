"use server";
import db from "@/lib/db";

export const updateExistingUserId = async (user: any) => {
  if (!user) {
    return;
  }
  try {
    const users = await db.user.upsert({
      where: {
        email: user.emailAddresses[0].emailAddress,
      },
      update: {
        id: user.id,
        name: user.firstName + " " + user.lastName,
      },
      create: {
        id: user.id,
        name: user.firstName + " " + user.lastName,
        email: user.emailAddresses[0].emailAddress,
      },
    });

    return user;
  } catch (error) {
    console.error("Error updating user id:", error);
    return;
  }
};
