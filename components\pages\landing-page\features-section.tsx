"use client";

import React from "react";
import Container from "@/components/core/container";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Palette,
  Smartphone,
  Zap,
  Shield,
  Globe,
  BarChart3,
  Layers,
  Users,
  Rocket,
} from "lucide-react";

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  highlight?: boolean;
}

export interface FeaturesSectionProps {
  className?: string;
}

const features: Feature[] = [
  {
    id: "drag-drop",
    title: "Drag & Drop Builder",
    description:
      "Intuitive visual editor that lets you build websites by simply dragging and dropping elements. No technical skills required.",
    icon: <Layers className="w-6 h-6" />,
    highlight: true,
  },
  {
    id: "responsive",
    title: "Mobile Responsive",
    description:
      "All websites automatically adapt to any screen size. Your site will look perfect on desktop, tablet, and mobile devices.",
    icon: <Smartphone className="w-6 h-6" />,
  },
  {
    id: "fast",
    title: "Lightning Fast",
    description:
      "Optimized for speed with modern web technologies. Your visitors will experience blazing-fast loading times.",
    icon: <Zap className="w-6 h-6" />,
    highlight: true,
  },
  {
    id: "secure",
    title: "Secure & Reliable",
    description:
      "Enterprise-grade security with SSL certificates, regular backups, and 99.9% uptime guarantee.",
    icon: <Shield className="w-6 h-6" />,
  },
  {
    id: "seo",
    title: "SEO Optimized",
    description:
      "Built-in SEO tools help your website rank higher in search results and attract more visitors.",
    icon: <BarChart3 className="w-6 h-6" />,
  },
  {
    id: "global",
    title: "Global CDN",
    description:
      "Your website is delivered from servers worldwide, ensuring fast loading times for visitors everywhere.",
    icon: <Globe className="w-6 h-6" />,
  },
  {
    id: "templates",
    title: "Beautiful Templates",
    description:
      "Choose from hundreds of professionally designed templates or start from scratch with complete creative freedom.",
    icon: <Palette className="w-6 h-6" />,
    highlight: true,
  },
  {
    id: "collaboration",
    title: "Team Collaboration",
    description:
      "Work together with your team in real-time. Share projects, assign roles, and collaborate seamlessly.",
    icon: <Users className="w-6 h-6" />,
  },
  {
    id: "deployment",
    title: "One-Click Deploy",
    description:
      "Publish your website instantly with a single click. Connect custom domains and go live in seconds.",
    icon: <Rocket className="w-6 h-6" />,
  },
];

const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section
      id="features"
      className={`py-24 bg-muted/30 ${className || ""}`}
      aria-labelledby="features-heading"
    >
      <Container>
        <div className="text-center space-y-4 mb-16">
          <h2
            id="features-heading"
            className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight"
          >
            Everything You Need to
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              {" "}
              Build Amazing Websites
            </span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
            Powerful features designed to help you create professional websites
            without any coding knowledge.
          </p>
        </div>

        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          role="list"
        >
          {features.map((feature) => (
            <Card
              key={feature.id}
              className={`group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 focus-within:ring-4 focus-within:ring-primary/20 ${
                feature.highlight
                  ? "border-primary/20 bg-gradient-to-br from-primary/5 to-transparent"
                  : "hover:border-primary/20"
              }`}
              role="listitem"
              tabIndex={0}
              aria-labelledby={`feature-title-${feature.id}`}
              aria-describedby={`feature-desc-${feature.id}`}
            >
              <CardHeader className="pb-4">
                <div
                  className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${
                    feature.highlight
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground group-hover:bg-primary group-hover:text-primary-foreground"
                  } transition-colors duration-300`}
                  aria-hidden="true"
                >
                  {feature.icon}
                </div>
                <CardTitle
                  id={`feature-title-${feature.id}`}
                  className="text-xl font-semibold"
                >
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p
                  id={`feature-desc-${feature.id}`}
                  className="text-muted-foreground leading-relaxed"
                >
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Container>
    </section>
  );
};

export default FeaturesSection;
