import { getProject } from "@/actions/projects";
import { Prisma } from "@prisma/client";

export enum StatusCode {
  Ok = 200,
  Created = 201,
  BadRequest = 400,
  Unauthorized = 401,
  Forbidden = 403,
  NotFound = 404,
  InternalServerError = 500,
}

export type ActionState<T> = {
  code: StatusCode;
  message: string;
  data?: T;
  error?: Error;
  success?: boolean;
};

export type ProjectWithAll = NonNullable<
  Prisma.PromiseReturnType<typeof getProject>
>;
