import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LetterText } from "lucide-react";
import {
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import SectionOptions from "./section-options";
import { Section } from "@prisma/client";

type Props = {
  children?: React.ReactNode;
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptionDialog = ({ children, ...rest }: Props) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="min-w-7xl max-h-screen overflow-y-auto">
        <DialogHeader className="text-center pb-4">
          <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
            <LetterText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Choose Your Section Type
          </DialogTitle>
          <p className="text-muted-foreground mt-2">
            Select the type of content you want to add to your website
          </p>
        </DialogHeader>
        <SectionOptions {...rest} />
        <DialogFooter className="pt-4">
          <DialogClose asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              Cancel
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SectionOptionDialog;
